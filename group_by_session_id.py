import pandas as pd
import json
from datetime import date
from collections import Counter
import os
import argparse

def convert_to_json_serializable(item):
    if isinstance(item, (date, pd.Timestamp)):
        return str(item)
    elif isinstance(item, dict):
        return {key: convert_to_json_serializable(value) for key, value in item.items()}
    elif isinstance(item, list):
        return [convert_to_json_serializable(sub_item) for sub_item in item]
    return item

def get_user_operation_data(df, output_json_path, args):
    fout = open(output_json_path, 'a')

    # 按 sessionid_ 分组，并对每个组按 time 排序
    grouped = df.groupby('short_sessionid_').apply(lambda x: x.sort_values(by='timestamp_')).reset_index(drop=True)

    # 创建一个字典，键为 sessionid_  对应的排序后的记录列表
    result_dict = {}
    for name, group in grouped.groupby('short_sessionid_'):
        result_dict[name] = group.to_dict(orient='records')

    # 输出结果
    length_dist = Counter()
    for key, value in result_dict.items():
        value = convert_to_json_serializable(value)
        length = len(value)
        length_dist[length] += 1
        if  args.trajectory_length_min <= length and length <= args.trajectory_length_max :
            fout.write(key + "\t*#&\t" + json.dumps(value, ensure_ascii=False) + '\n')

    for i in range(len(length_dist)):
        print(i, " ",  length_dist[i])

def get_session_id(s):
    #hash=802408136&ts=1743774812155&host=&version=671103404&device=2#109385745#1743774845601
    return s.strip().split("#")[0]


def get_timestamp(s):
    return int(s.strip().split("#")[-2])


def group_by_session_id(args):
    pickle_file_path = args.input_pickle_dir
    output_json_path = args.output_json_path

    target_pickles_str = args.target_pickles
    target_pickles = []
    if target_pickles_str == "all":
        target_pickles = [f for f in os.listdir(pickle_file_path) if f.endswith("pickle")]
    else:
        target_pickles = target_pickles_str.strip().split(",")

    print("target_pickles is ", target_pickles)
    # pickle数据group by 之后保存成json格式
    file_list = [os.path.join(pickle_file_path, f) for f in target_pickles]
    for file_path in file_list:
        print("Processing ... ", file_path)
        df = pd.read_pickle(file_path)
        # 查看数据基本信息
        print("数据基本信息：")
        df.info()

        # 查看数据集行数和列数
        rows, columns = df.shape
        print(rows, columns)
        print("前几行信息：")
        #print(df.head().to_csv(sep='\t', na_rep='nan'))
        print(df.head())
        cnt = 0
        for index, row in df.iterrows():
            print(row['sessionid_'])
            print(f"Index: {index}, appid_: {row['appid_']}, sessionid_: {row['sessionid_']}, clickitem_: {row['clickitem_']}")
            cnt += 1
            if cnt > 10: break
        # 提取sessionid_
        df['short_sessionid_'] = df['sessionid_'].apply(get_session_id)
        df['timestamp_'] = df['sessionid_'].apply(get_timestamp)

        get_user_operation_data(df, output_json_path, args)


def split_txt_file(args):
    num_parts = args.split_num
    file_path = args.output_json_path
    try:
        # 读取源文件的所有行
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 计算总行数
        total_lines = len(lines)

        # 计算每个小文件应包含的行数
        lines_per_part = total_lines // num_parts

        # 分割文件
        for i in range(num_parts):
            start_index = i * lines_per_part
            end_index = start_index + lines_per_part if i < num_parts - 1 else total_lines

            # 确定小文件的文件名
            output_file_path = os.path.join(args.split_target_dir, f'part_{i + 1}.txt')

            # 写入小文件
            with open(output_file_path, 'w', encoding='utf-8') as output_file:
                output_file.writelines(lines[start_index:end_index])

        print(f'文件已成功分割成 {num_parts} 个小文件。')
    except FileNotFoundError:
        print(f"错误：未找到文件 {file_path}。")
    except Exception as e:
        print(f"发生未知错误：{e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MiniP data Group by session")
    parser.add_argument("--input_pickle_dir", type=str, help="input_pickle_dir", default="")
    parser.add_argument("--target_pickles", type=str, help="all|file1,file2,file3",
                        default="all")

    parser.add_argument("--trajectory_length_min", type=int, help="trajectory_length_min",
                        default=1)
    parser.add_argument("--trajectory_length_max", type=int, help="trajectory_length_max",
                        default=20)
    parser.add_argument("--output_json_path", type=str, help="output_json_path",
                        default="")

    parser.add_argument("--split_num", type=int, help="split_num",
                        default=10)
    parser.add_argument("--split_target_dir", type=str, help="split_target_dir",
                        default="")
    parser.add_argument("--function", type=str, help="function",
                        default="group_by_session_id")


    print("Start....")
    args = parser.parse_args()
    print("args is : ")
    print(args)

    if args.function == "group_by_session_id":
        if os.path.exists(args.output_json_path):
            print(f"{args.output_json_path} has been exists, please make sure")
            exit(0)
        group_by_session_id(args)
    elif args.function == "split_txt_file":
        if os.path.exists(args.split_target_dir):
            print(f"{args.split_target_dir} has been exists, please make sure")
            exit(0)
        os.makedirs(args.split_target_dir)
        split_txt_file(args)


import argparse
import pandas as pd
import os
#os.system('pip3 install rawins --upgrade -i https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple --extra-index-url https://mirrors.tencent.com/pypi/simple/')
import gzip
import base64
import csv, json, sys, requests

from rawins import Rawins
from pandarallel import pandarallel
from io import BytesIO, <PERSON><PERSON>
from typing import List, Dict, Any
from Crypto.Cipher import AES
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
from utils import load_private_key_from_base64, decrypt_with_rsa, decode_with_pycryptodome
from datetime import datetime, timedelta
from py_mini_racer import MiniRacer

pandarallel.initialize(progress_bar=False)

# db_conf
mysql_conf = {
    "user": "root",
    "passwd": "9g@gmZh7Hf",
    "port": 3306,
    "host": "***********",
    "db": "mmfd_zhao<PERSON>",
    "charset": 'utf8'
}

# # clickhouse conf
ch_conf = {
"host":'**************',
"port": 9000,
"user": 'longvideo_luban',
"password": 'GZqtkM9yG4N6ZKbYsa7r',
"database": 'default' ,
"send_receive_timeout": 120
}

rawins = Rawins(ch_conf, mysql_conf)
rawins.register("marcusdai")

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option("display.max_colwidth", None)

credential_file_path = "/root/.credential/credential.rsa"
private_key = load_private_key_from_base64(credential_file_path)

def reverse_kotlin_operation(processed_str: str) -> str:
    print('len:', len(processed_str))
    try:
        compressed_bytes = base64.b64decode(processed_str)
    except Exception as e:
        print("Base64解码失败") 
        return None
    try:
        with gzip.GzipFile(fileobj=BytesIO(compressed_bytes)) as f:
            return f.read().decode('utf-8')
    except gzip.BadGzipFile as e:
        print("GZIP解压失败")
        return None


def process_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    # 1. 拼接所有列（处理可能的空值）
    df['combined'] = df.apply(
        lambda row: ''.join([
            str(row[f'xml{i}_']) 
            for i in range(1, 7) 
            if pd.notna(row[f'xml{i}_']) and str(row[f'xml{i}_']) != ''
        ]),
        axis=1
    )

    df['secret_key'] = df['key_'].parallel_apply(lambda x: decrypt_with_rsa(private_key, x))
    # 2. 逆向操作
    df['combined'] = df.parallel_apply(lambda row: decode_with_pycryptodome(row['combined'], row['secret_key']),axis=1)
    df['clickitem_'] = df.parallel_apply(lambda row: decode_with_pycryptodome(row['clickitem_'], row['secret_key']),axis=1)
    df['nativeinfo_'] = df.parallel_apply(lambda row: decode_with_pycryptodome(row['nativeinfo_'], row['secret_key']),axis=1)
    df['nickname_'] = df.parallel_apply(lambda row: decode_with_pycryptodome(row['nickname_'], row['secret_key']),axis=1)
    df['signature_'] = df.parallel_apply(lambda row: decode_with_pycryptodome(row['signature_'], row['secret_key']),axis=1)
    df['categories_'] = df.parallel_apply(lambda row: decode_with_pycryptodome(row['categories_'], row['secret_key']),axis=1)
    
    # 3. 清理中间列（可选）
    cols_to_drop = [f'xml{i}_' for i in range(1, 7)]
    df = df.drop(columns=[col for col in cols_to_drop if col in df.columns])
    return df

def decode_csv_with_js(df):
    # 初始化 JavaScript 环境（只初始化一次）
    ctx = MiniRacer()
    js_code = ""
    # 读取 JavaScript 代码
    with open("./js/index_xml_0527.global.js", "r", encoding='utf-8') as file:
        js_code = file.read()
    ctx.eval(js_code)
    # 使用 apply 应用 JavaScript 函数
    def decode_func(x):
        try:
            return ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", x) if pd.notna(x) else None
        except Exception as e:
            print(f"Error occurred for value: {x}")  # 打印报错的值
            print(e)
            return None
    
    df['decoded'] = df['combined'].parallel_apply(decode_func)
    return df


def new_process_dataframe(df):
    df[['decoded', 'clickitem_']] = df.parallel_apply(process_row, axis=1)
    return df

def process_row(row):
    native_info = row['nativeinfo_']
    decoded = row['decoded']
    if native_info and len(row.get('clickitem_', None)) == 0:
        # native_info is sth like '<native><tab-bar id=xxx>...</tab-bar></native>'
        new_html = f"""
        <page>
            <iframe>
                {decoded}
            </iframe>
            {native_info}
        </page>
        """
        return pd.Series([new_html, row.get('clickitem_', None)], index=['decoded', 'clickitem_'])
    return pd.Series([decoded, row.get('clickitem_', None)], index=['decoded', 'clickitem_'])


def get_pickled_data(output_file_name: str, session_cgi_label: str = "cgi7"):
    # 打印当前时间
    init_time = datetime.now()
    print(f"当前时间：{init_time.strftime('%Y-%m-%d %H:%M:%S')}")
    # 获取当前日期的前一天
    yesterday = datetime.now() - timedelta(days=1)
    start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)  # 前一天的 0 点
    end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)  # 前一天的 24 点
    # 动态生成 SQL 查询
    sql = f"""
    select * from dw_luban.log_34771 
    where hour_ >= toDateTime('{start_time.strftime('%Y-%m-%d %H:%M:%S')}')
    AND hour_ < toDateTime('{end_time.strftime('%Y-%m-%d %H:%M:%S')}')
    and sessionid_ LIKE '%#{session_cgi_label.upper()}%'
    """

    print(sql)

    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    # 打印获取到数据的时间
    got_data_time = datetime.now()
    print(f"获取到数据的时间：{got_data_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"获取数据耗时：{got_data_time - init_time}")

    print(df.shape)
    print(df.columns)
    # print(df[['second_', 'appid_', 'sessionid_', 'length_', 'nativeinfo_','key_']])

    df = process_dataframe(df)
    # 处理df的耗时
    processed_time = datetime.now()
    print(f"处理数据的时间：{processed_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"处理数据耗时：{processed_time - got_data_time}")
    print(df.shape)
    df = df[df[['combined', 'clickitem_', 'nativeinfo_']].notna().all(axis=1)]
    print('filter None: ', df.shape)
    df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'nickname_', 'signature_', 'categories_']]

    df = decode_csv_with_js(df)
    # 解码耗时
    decoded_time = datetime.now()
    print(f"解码数据的时间：{decoded_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"解码数据耗时：{decoded_time - processed_time}")
    df = df[df[['decoded']].notna().all(axis=1)]
    print('after decoded:', df.shape)
    print('====='*5)
    # print(df['clickitem_'])
    print('====='*5)
    # print(df['combined'])
    print('====='*5)
    #print(df['decoded'])
    df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'decoded',  'nickname_', 'signature_', 'categories_']]
    print(df.shape)
    df = df[df.length_ < 1024*6]
    print('filter length', df.shape)

    df = new_process_dataframe(df)
    # new process 耗时
    new_process_time = datetime.now()
    print(f"新处理数据的时间：{new_process_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"新处理数据耗时：{new_process_time - decoded_time}")
    print(df.shape)

    # 保存 DataFrame 到动态生成的文件名
    df.to_pickle(output_file_name)

    print(f"DataFrame 已保存到文件: {file_name}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Get Pickled Data")
    parser.add_argument("--output_file_name", type=str, help="output_file_name")
    parser.add_argument("--session_cgi_label", type=str, help="session_cgi_label", default="cgi7")

    print("Start....")
    args = parser.parse_args()
    print("args is : ")
    print(args)
    file_name = args.output_file_name
    if not file_name.endswith(".pickle"):
        file_name += ".pickle"
    get_pickled_data(file_name, args.session_cgi_label)

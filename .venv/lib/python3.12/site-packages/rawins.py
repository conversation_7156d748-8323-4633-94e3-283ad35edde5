import pymysql
import re
import clickhouse_driver as ch
import pandas as pd
import time
from collections import OrderedDict
import socket
import random
import requests
import json
import sqlparse
from sqlparse.sql import IdentifierList, Identifier
from sqlparse.tokens import Keyword, DML
import socket
import urllib
from functools import wraps
import datetime
import traceback

__query_report_name__ = "select name, report_index, status, type, update_time, rtx from report_index_long;"
__re_table_name_pattern__ = re.compile("\$\w+") # "$[^A-Za-z0-9_]"
# type in db
type_dict = {
             0 : "Feat",
             1 : "Label"
             }

support_table = ["default.pc_a_dist","$pc_a",
                 "default.pc_c_dist", "$pc_c",
                 "default.pc_a_v2_dist", "$pc_a_v2",
                 "default.pc_c_v2_dist", "$pc_c_v2",
                 "default.pc_a_new_dist", "$pc_a_new",
                 "default.pc_c_new_dist", "$pc_c_new",
                 "default.pc_c_replay_dist", "$pc_c_replay",]
# table alias
tables = OrderedDict([
    ("$reddot_b", "default.reddot_b_dist"),
    ("$reddot_ac", "default.reddot_ac_dist"),
    ("$reddot_abc", "default.reddot_abc_dist"),
    ("$reddot_a_v2", "default.reddot_a_dist"),
    ("$reddot_a", "default.reddot_dist"),
    ("$reddot_c", "default.reddot_c_dist"),
    ("$table_ac", "default.table_ac_dist"),
    ("$table_abc", "default.table_abc_dist"),
    ("$table_ab", "default.table_ab_dist"),
    ("$table_a", "default.rawins_dist"),
    ("$table_b", "default.md_offline_logits_dist"),
    ("$table_c", "default.feed_online_data_dist"),
    ("$friend_a", "default.friend_dist"),
    ("$hot_ac", "default.hot_ac_dist"),
    ("$hot_ab", "default.hot_ab_dist"),
    ("$hot_a", "default.hot_a_dist"),
    ("$hot_b", "default.hot_b_dist"),
    ("$hot_c_replay", "default.hot_c_replay_dist"),
    ("$hot_c", "default.hot_c_dist"),
    ("$share_a", "default.share_a_dist"),
    ("$review", "default.review_dist"),
    ("$pc_a_new", "default.pc_a_new_dist"),
    ("$pc_c_new", "default.pc_c_new_dist"),
    ("$pc_c_replay", "default.pc_c_replay_dist"),
    ("$pc_a", "default.pc_a_dist"),
    ("$pc_c", "default.pc_c_dist"),
    ("$pc_c_replay", "default.pc_c_replay_dist"),
    ("$mmdata_22217", "default.mmdata_22217_dist"),
    ("$mmdata_18913", "default.mmdata_18913_dist"),
    ("$mmdata_18690", "default.mmdata_18690_dist"),
    ("$mmdata_18691", "default.mmdata_18691_dist"),
    ("$finder_search_click", "default.finder_search_click_dist"),
    ("$session", "default.session_dist"),
    ("$finder_search_click", "default.finder_search_click_dist")
])


# tracing
# cube webservice
# inner net host
# cube_report_host = "http://api.cube.weixin.oa.com"
# ourside hostname
cube_report_host = "http://api.cube.weixin.oa.com"
cube_report_path = "/cube/report/reportbizdata?f=json"


# for click tools
clickhouse_helper_scene_id = 5003
clickhouse_helper_scene = "click_house_rawins_tools"
clickhouse_helper_report_from = "click_house_rawins_tools"
clickhouse_helper_service = "click_house_rawins_tools"
clickhouse_helper_reqfrom = "rawins_query_usage"

clickhouse_usage_report_key = "report_items="
PER_QUERY = "per_query"
PER_TABLE = "per_table"
PER_COLUMN = "per_column"

default_mysql_conf = {
    "user": "root",
    "passwd": "9g@gmZh7Hf",
    "port": 3306,
    "host": "***********",
    "db": "mmfd_zhaohui",
    "charset": 'utf8'
}

# clickhouse conf
default_ch_conf = {
    "host":'**************',
    "port": 9000,
    "user": 'reader',
    "password": 'VideochannelReader@2021',
    "database": 'default' ,
    "send_receive_timeout": 120
}

class TimeValidUtils:
    def extract_ds(self, sql):
        ds_pattern = "Ds.*'\d{4}-\d{2}-\d{2}'\]*" #'YYYY-MM-DD'
        value = re.findall(ds_pattern, sql)
        return value

    def extract_timestamp(self, sql):
        timestamp_pattern = "TimeStamp.*'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'"  # 'YYYY-MM-DD HH:MM:SS'
        value = re.findall(timestamp_pattern, sql)
        return value

    def validate_sytax(self, rule):
        if not rule:
            return False
        support_ops = ['>=', ">", "=", "<", "<=", "!=", "in", "not in"]
        flag = False
        for op in support_ops:
            if op in rule:
                flag = True
        return flag

    def validate_ds(self, ds, rules):
        if not ds:
            return True
        timestamp_pattern = "\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}"  # 'YYYY-MM-DD HH:MM:SS'
        ds_date = ds
        ds = str(ds)
        value = re.findall(timestamp_pattern, ds)
        if len(value) == 0:
            return False
        sytax_flag = False
        for rule in rules:
            # print(rules)
            sytax_flag = self.validate_sytax(rule)
            if not sytax_flag:
                return False
            # print("myds")
            # print(ds)
            temp_ds = "'" + datetime.datetime.strptime(ds, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d') + "'"

            # check in and not in
            if "in" in rule:
                dates = re.findall(r"(\d{4}-\d{2}-\d{2})", rule)
                for each_date in dates:
                    each_date = datetime.datetime.strptime(each_date, '%Y-%m-%d')
                    if (ds_date >= each_date):
                        return False
                        break

            rule = rule.replace("Ds", temp_ds)
            equal_pattern = "'\d{4}-\d{2}-\d{2}'\s*=\s*'\d{4}-\d{2}-\d{2}'"
            if len(re.findall(equal_pattern, rule)) != 0:
                rule = rule.replace("=", "==")
            try:
                time_check = eval(rule)
            except:
                return True

            if time_check:
                return False
        return True

    def validate_timestamp(self, ds, rules):
        if not ds:
            return True
        timestamp_pattern = "'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'"  # 'YYYY-MM-DD HH:MM:SS'
        ds = str(ds)
        value = re.findall(timestamp_pattern, ds)
        if len(value) == 0:
            return True
        sytax_flag = False
        for rule in rules:
            sytax_flag = self.validate_sytax(rule)
            if not sytax_flag:
                return False
            ds = "'" + datetime.datetime.strptime(ds, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S') + "'"
            rule = rule.replace("TimeStamp", ds)

            equal_pattern = "'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'\s*=\s*'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'"
            if len(re.findall(equal_pattern, rule)) != 0:
                rule = rule.replace("=", "==")
            try:
                time_check = eval(rule)
            except:
                return True
            if time_check:
                return False
        return True



# utils function
def is_subselect(parsed):
    if not parsed.is_group:
        return False
    for item in parsed.tokens:
        if item.ttype is DML and item.value.upper() == 'SELECT':
            return True
    return False


def extract_from_part(parsed):
    from_seen = False
    for item in parsed.tokens:
        if from_seen:
            if is_subselect(item):
                yield from extract_from_part(item)
            elif item.ttype is Keyword:
                return
            else:
                yield item
        elif item.ttype is Keyword and item.value.upper() == 'FROM':
            from_seen = True


def extract_table_identifiers(token_stream):
    for item in token_stream:
        if isinstance(item, IdentifierList):
            for identifier in item.get_identifiers():
                yield identifier.get_name()
        elif isinstance(item, Identifier):
            yield item.get_name()
        # It's a bug to check for Keyword here, but in the example
        # above some tables names are identified as keywords...
        elif item.ttype is Keyword:
            yield item.value


def extract_tables(sql):
    stream = extract_from_part(sqlparse.parse(sql)[0])
    return list(extract_table_identifiers(stream))



class TraceManager:
    def __init__(self, name = "default"):
        self.operator = name


    def build_item_query_statical(self, time_cost, table_name, column_name = "all", type = "default", report_operator = "default", sql = "default"):
        report_item = {}
        report_item["biz_id"] = clickhouse_helper_scene_id
        report_item["time"] = int(time.time())
        report_item["report_ip"] = str(self.get_host_ip())
        report_item["scene"] = clickhouse_helper_scene
        report_item["report_from"] = clickhouse_helper_report_from
        report_item["service"] = clickhouse_helper_service
        report_item["req_count"] = 1
        report_item["req_cost"] = int(time_cost*100)
        report_item["req_name"] = clickhouse_helper_reqfrom
        report_item["table_name"] = table_name
        report_item["column_name"] = column_name
        # clickhouse_helper_reqfrom
        # table name
        # column name
        report_item["type"] = type
        report_item["operator"] = report_operator
        if (sql != "default"):
            report_item["sql_string"] = sql


        return report_item



    '''
    batch build payload json by items
    :param items, list of each item
    :return json string
    '''
    def build_payload(self, items = []):
        msg = clickhouse_usage_report_key + json.dumps(items)
        return msg


    def trace_usage(self, payload):
        host = cube_report_host
        path = cube_report_path
        try:
            response = requests.post(host + path, data = payload)
        except:
            print("Info: trace report failed.")


    def get_host_ip(self):
        """
        query hostname
        :return:
        """
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            ip = s.getsockname()[0]
        finally:
            s.close()

        return ip



class NameWrapper:
    def __init__(self, mysql_conf):
        try:
            self.conf = mysql_conf
            self.init_flag = False
        except:
            print("Error: Connect to db failed, please check network connection")

    def query(self, sql_phrase):
        try:
            self.conn = pymysql.Connect(**self.conf)
            self.curr = self.conn.cursor()
            result_num = self.curr.execute(sql_phrase)
            result = self.curr.fetchall()
            self.curr.close()
            self.conn.close()
            return result
        except:
            print("Error: query failed, please check network connection")

    '''
    :return tuple({alias_name, {name, status, datatype}},{alias_name, {name, status, datatype}})
    :note status 1 means field in use and 0 means available, can be found in 
    '''
    def get_name_dict(self, refresh = True):
        if (self.init_flag == False or refresh == True):
            data = self.query(__query_report_name__)
            self.cache_data = data
            self.init_flag = True
        else:
            data = self.cache_data
        if not data:
            print("Error: query alias name failed, using raw table name")
            return None

        name_value = {}
        for name, report_index, status, data_type, update_time, rtx_amend in data:
            if name and name != "":
                name_value[name] = (str(type_dict.get(data_type)) + str(report_index).zfill(3), status, data_type, update_time, rtx_amend)
        # name_value = {name: (str(type_dict.get(data_type)) + str(report_index).zfill(3), status, data_type) for name, report_index, status, data_type in data}
        value_name = {str(type_dict.get(data_type)) + str(report_index).zfill(3) : (name, status, data_type, update_time, rtx_amend) for name, report_index, status, data_type, update_time, rtx_amend in data}

        name_value = OrderedDict(sorted(name_value.items(), key=lambda item: item[0]))


        return name_value, value_name

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
        # self.curr.close()
        # self.conn.close()





class Rawins:
    def __init__(self, ch_conf, mysql_conf):
        # clickhouse client
        self.__ch_client = ch.Client(**ch_conf)
        # db wrapper
        self.__name_wrapper = NameWrapper(mysql_conf)

        # operator
        self.operator = "default"
        self.passwd = "default"
        self.init_table_a = False
    
#    def __init__(self):
#        # clickhouse client
#        self.__ch_client = ch.Client(**default_ch_conf)
#        # db wrapper
#        self.__name_wrapper = NameWrapper(default_mysql_conf)

        # operator
#        self.operator = "default"
#        self.passwd = "default"
#        self.init_table_a = False

    def register(self, operator = "default", passwd = "default"):
        self.operator = operator
        self.passwd = passwd

    @property
    def Ds(self):
        return self._ds

    @Ds.setter
    def Ds(self, ds):
        if ds and not re.match("^\d{4}-\d{2}-\d{2}$", ds):
            raise ValueError("param ds must like 'yyyy-mm-dd'")
        self._ds = ds

    @Ds.deleter
    def Ds(self):
        self._ds = None

    def replace_fileds(self, sql, refresh = True, table_name = "", mannual_report = ""):
        if not sql:
            return sql
        if mannual_report != "":
            for table_name, real_table_name in tables.items():
                sql = sql.replace(table_name, real_table_name)
            for k, v in mannual_report.items():
                sql = re.sub(r"\b%s\b" % k, v, sql)
            return sql
        try:
            (name_map, map_name) = self.__name_wrapper.get_name_dict(refresh = refresh)
        except:
            return sql
        # replace table name
        for table_name, real_table_name in tables.items():
            sql = sql.replace(table_name, real_table_name)

        #replace fileds
        (name_value, value_name) = self.__name_wrapper.get_name_dict(refresh = refresh)

        # get table raw name, raw name prefered
        # to slove this issue: https://git.woa.com/penguinqian/drbasic/issues/73
        table_raw_name = self.get_table_a_raw_name(sql)
        for alias, raw_name in name_value.items():
            col_names = re.findall(r"\b%s\b" % alias, sql)
            if (len(col_names) > 0 and col_names[0] in table_raw_name):
                print("\033[7;31m---------------------WARNING----------------------------\033[7;31m")
                print("列名「" +  alias + "」和Clickhouse原生表字段「" + alias + "」重复，优先使用原生字段，如果想要查询注册Feat/Label,请使用字段：「" + raw_name[0] + "」或者联系字段owner，他的字段和表预留公参重复了")
                print('\033[0m')
                continue
            sql = re.sub(r"\b%s\b" % alias, raw_name[0], sql)
        return sql

    def get_table_a_raw_name(self, raw_sql):

        sql  = "desc default.pc_a_new_dist"
        table_names = extract_tables(raw_sql)
        table = ""
        if ("pc_a_new_dist" in table_names) :
            table = "default.pc_a_new_dist"
        table_raw_name = set()
        if (table != "" ):
            if (self.init_table_a == False):
                table_result = self.__ch_client.query_dataframe(f"desc {table}")
                for item in table_result['name']:
                    table_raw_name.add(item)
                self.init_table_a = True
                self.table_a_raw_name = table_raw_name
            else:
                table_raw_name = self.table_a_raw_name
        else:
            return table_raw_name




        return table_raw_name



    def report_usage(self, sql, time_cost,pretty = True, refresh = True):
        trace = TraceManager()
        items = []

        if not sql:
            return sql
        try:
            (name_map, map_name) = self.__name_wrapper.get_name_dict(refresh = refresh)
        except:
            return sql
        if pretty:
            print("pretty display raw:")
            print(sqlparse.format(sql, reindent = True))
        # report per query
        items.append(trace.build_item_query_statical(time_cost, table_name = "default", type = PER_QUERY, sql = sql, report_operator = self.operator))
        # replace table name
        for table_name, real_table_name in tables.items():
            sql = sql.replace(table_name, real_table_name)

        # per table
        used_table = extract_tables(sql)
        # print("\033[7;31m---------------------TABLE NAME---------------------------------------------\033[7;31m")
        # print(used_table)
        # print('\033[0m')
        for table in used_table:
            items.append(trace.build_item_query_statical(time_cost, table_name = table, type = PER_TABLE, report_operator = self.operator))

        (name_value, value_name) = self.__name_wrapper.get_name_dict(refresh = refresh)

        # per column
        for table in used_table:
            for name, alias_name in value_name.items():
                if name and name in sql and alias_name[0] != "":
                    # print(
                    #     "\033[7;31m---------------------COLUMN NAME---------------------------------------------\033[7;31m")
                    # print(alias_name[0])
                    # print('\033[0m')
                    items.append(trace.build_item_query_statical(time_cost, table_name = table, column_name = alias_name[0], type = PER_COLUMN, report_operator = self.operator))

        msg = trace.build_payload(items)
        try:
            trace.trace_usage(msg)
        except:
            print("trace: report error, check network")




    def valid_time(self, sql, refresh = True):
        if not sql:
            return sql
        try:
            (name_map, map_name) = self.__name_wrapper.get_name_dict(refresh = refresh)
        except:
            return sql

        checker = TimeValidUtils()
        ds_range = checker.extract_ds(sql)
        timestamp_range =   checker.extract_timestamp(sql)

        collects = {}
        for name, alias_name in name_map.items():
            if name in sql and alias_name != "":
                # print(alias_name, name)
                ds_ret = checker.validate_ds(alias_name[3], ds_range)

                timestamp_ret = checker.validate_timestamp(alias_name[3], timestamp_range)

                if not ds_ret or not timestamp_ret:
                    print("\033[7;31m---------------------WARNING:以下列在查询时间范围内索引有变更，请注意结果有效性----------------------\033[7;31m")
                    print(name, alias_name[0], alias_name[3], alias_name[4])
                    print('\033[0m')







        # #replace fileds
        # (name_value, value_name) = self.__name_wrapper.get_name_dict()
        #
        # for alias, raw_name in name_value.items():
        #     sql = re.sub(r"\b%s\b" % alias, raw_name[0], sql)
        # return sql

    '''
    TODO: not use.
    :note: replace alias wrapper if use_alias is true(default is true)
    '''
    # def alias_replace(func):
    #     @wraps(func)
    #     def wrapper(self, *args, **kwargs):
    #         sql = args[0] if args else kwargs("sql")
    #         use_alias = kwargs.get("use_alias", False)
    #         if use_alias:
    #             if not sql:
    #                 raise ValueError("sql lack of sql param")
    #             if "Ds" not in sql:
    #                 raise ValueError("sql lack of Ds filter")
    #             # res = re.findall(__re_table_name_pattern__, sql)
    #             # in case of long query
    #             if "SELECT * FROM" in sql.upper() and "LIMIT" not in sql.upper():
    #                 print("warning: limit 10 add to sql in case of long query, if you wanna to use, please use filed name, or don't use alias replace")
    #                 sql += "limit 10"
    #
    #
    #     return wrapper



    def execute(self, sql, table_name = None, use_alias = True):
        res = self.__ch_client.execute(sql)
        return res

    def query_dataframe(self, sql , use_alias = True, pretty = True, valid_time = True, report = True, refresh = True, table_name = "", mannual_report = ""):
        if pretty:
            print("pretty display:")
            print(sqlparse.format(sql, reindent = True))
        if valid_time:
            self.valid_time(sql, refresh = refresh)

        if use_alias:
            sql = self.replace_fileds(sql, refresh = refresh, table_name = table_name, mannual_report = mannual_report)

        start_time = time.time()
        res = self.__ch_client.query_dataframe(sql)
        end_time = time.time()

        if report:
            self.report_usage(sql,  time_cost = end_time - start_time, pretty=pretty, refresh = refresh)
        raw_cols = list(res.columns)
        
        if (use_alias == False and mannual_report == ""):
            return res

        if (mannual_report != ""):
            for i in range(0, len(raw_cols)):
                for k, v in mannual_report.items():
                    if raw_cols[i] == v:
                      raw_cols[i] = raw_cols[i].replace(raw_cols[i], k)
            res.columns = raw_cols
            return res


        (name_value, value_name) = self.__name_wrapper.get_name_dict(refresh = refresh)

        for i in range(0, len(raw_cols)):
            alias_name = value_name.get(raw_cols[i], None)
            if alias_name and alias_name[0] != "" and alias_name[0]:
                raw_cols[i] = raw_cols[i].replace(raw_cols[i], alias_name[0])
        res.columns = raw_cols
        return res

    '''
    :param select_columns: must be list of several colums, support funcs including ["count(col)", "uniq(col)", "avg(col)", "sum(col)", "max(col)", "min(col)", "median(col)", "quantile(p)(col)"]
    :param group_by_columns: must be list of several colums
    :param condition_columns: must be list of several condition
    :param order_by_columns:
    :param limit: must be a str like "a[, b]" or a number
    :param use_cache: if use_cache, will query redis first, return val if hit.if not hit, will query clickhouse,and result will be cached for next 6 hours.
    :return: result
    '''
    def query(self, table_name, select_columns, group_by_columns=[], condition_columns=[], order_by_columns=[], limit=200, pretty = True, refresh = True):
        selects = ",".join(select_columns)
        group_by = ",".join(group_by_columns)
        new_condition_columns = ([f"Ds = '{self._ds}'"] + condition_columns) if self._ds else condition_columns
        conditions = " and ".join(new_condition_columns)
        order_by = ",".join(order_by_columns)
        sql = f"select {selects} from {table_name} where {conditions}"
        if group_by:
            sql += " group by " + group_by
        if order_by:
            sql += " order by " + order_by
        sql += f" limit {limit}"
        
        if table_name in support_table:
            sql = self.replace_fileds(sql, refresh = refresh)
        self.report_usage(sql, time_cost = 0, pretty = pretty, refresh = refresh)
        return self.execute(sql=sql)


    '''
    :param tabale_name:alias_table name, can be find front, name alias of clickhouse raw table name
    :param use_alias, use alias name, default true, if you want to use raw name, please set false
    :param show_unused: display unused field if set true, default is false  
    '''
    def show_table(self, table_name=None, show_unused = False, use_alias=True, mannual_report = ""):
        if not table_name:
            _table_name = "default.pc_a_dist"
        else:
            _table_name = tables.get(table_name, table_name)
        if not _table_name:
            print(f"no table named {table_name}")

        start_time = time.time()
        df = self.__ch_client.query_dataframe(f"desc {_table_name}")
        end_time = time.time()
        trace = TraceManager()
        items = []
        items.append(trace.build_item_query_statical(sql = f"desc {_table_name}", table_name= _table_name, time_cost = end_time - start_time, type = PER_QUERY, report_operator = self.operator))
        trace.trace_usage(trace.build_payload(items))

        df = df.filter(items=['name', 'type'])

        if (mannual_report != "") :
            for k, v in mannual_report.items():
                df["name"] = df["name"].map(lambda x: k if x==v else x)
                # status != 0, means in using
                df = df.loc[df['name'] != "", :]
            pd.set_option('display.max_columns', None)
            pd.set_option('display.max_rows', None)
            return df

        if use_alias and not show_unused:
            data = self.__name_wrapper.get_name_dict()
            if data:
                df["name"] = df["name"].map(lambda x : data[1].get(x, None)[0] if data[1].get(x, None)  else x)
                # status != 0, means in using
                df = df.loc[df['name'] != "", :]

        # display all
        pd.set_option('display.max_columns', None)
        pd.set_option('display.max_rows', None)
        return df


    def exec_sql(self, sql = '', todf = True, debug = False, cluster = "mmfinderbdeolap"):
        req = {
            "app_name": "mmfinderdata",
            "cluster": cluster,
            "sql": sql
        }
        try:
            resp = requests.post("http://fbf.bdefinder.wx.com:23699/http_query_ch", json=req)
            data = json.loads(resp.text)['data']
            if todf:
                data = pd.DataFrame(data)
            return data, resp.status_code
        except Exception:
            return None, traceback.format_exc()

    def query_dataframe_metis(self, sql = '', todf = True, debug = False, cluster = "mmfinderbdeolap", report = True):
        pd.set_option('display.max_columns', None)
        pd.set_option('display.max_rows', None)
        start_time = time.time()
        data, err = self.exec_sql(sql, todf, debug, cluster)
        if (err != 200):
            print("\033[7;31mERROR CODE:\033[7;31m" + err)
        if report:
            end_time = time.time()
            self.report_usage(sql,  time_cost = end_time - start_time, pretty = debug, refresh = False)
        return data

    def show_table_metis(self, table_name = 'mmfinder.tbl_sess2_mptl_card_v2_dist'):
        sql = 'desc ' + table_name
        return self.query_dataframe_metis(sql);

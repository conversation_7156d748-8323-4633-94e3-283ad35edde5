Metadata-Version: 2.4
Name: pandarallel
Version: 1.6.5
Summary: An easy to use library to speed up computation (by parallelizing on multi CPUs) with pandas.
Home-page: https://nalepae.github.io/pandarallel
Author: Manu NALEPA
Author-email: <EMAIL>
License: BSD
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Scientific/Engineering
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: dill>=0.3.1
Requires-Dist: pandas>=1
Requires-Dist: psutil
Provides-Extra: dev
Requires-Dist: numpy; extra == "dev"
Requires-Dist: pytest; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Provides-Extra: doc
Requires-Dist: mkdocs-material; extra == "doc"
Dynamic: license-file

# Pandaral·lel

[![PyPI version fury.io](https://badge.fury.io/py/pandarallel.svg)](https://pypi.python.org/pypi/pandarallel/)
[![PyPI license](https://img.shields.io/pypi/l/pandarallel.svg)](https://pypi.python.org/pypi/pandarallel/)
[![PyPI download month](https://img.shields.io/pypi/dm/pandarallel.svg)](https://pypi.python.org/pypi/pandarallel/)

| Without parallelization  | ![Without Pandarallel](https://github.com/nalepae/pandarallel/blob/master/docs/progress_apply.gif?raw=true)       |
| :----------------------: | ----------------------------------------------------------------------------------------------------------------- |
| **With parallelization** | ![With Pandarallel](https://github.com/nalepae/pandarallel/blob/master/docs/progress_parallel_apply.gif?raw=true) |

**Pandaral.lel** provides a simple way to parallelize your pandas operations on all your
CPUs by changing only one line of code. It also displays progress bars.

## Maintainers
- [Manu NALEPA](https://github.com/nalepae/)
- [till-m](https://github.com/till-m)

## Installation

```bash
pip install pandarallel [--upgrade] [--user]
```

## Quickstart

```python
from pandarallel import pandarallel

pandarallel.initialize(progress_bar=True)

# df.apply(func)
df.parallel_apply(func)
```

## Usage

Be sure to check out the [documentation](https://nalepae.github.io/pandarallel).

## Examples

An example of each available `pandas` API is available:

- For [Mac & Linux](https://github.com/nalepae/pandarallel/blob/master/docs/examples_mac_linux.ipynb)
- For [Windows](https://github.com/nalepae/pandarallel/blob/master/docs/examples_windows.ipynb)
